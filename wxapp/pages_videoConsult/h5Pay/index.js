Page({
  data: {},
  onLoad(options) {
    console.log('高济H5支付页面参数:', options)

    // 兼容两种参数传递方式
    let paymentParams = {}

    // 方式1: 高济H5直接传递微信支付参数 (新方案)
    if (options.timeStamp && options.nonceStr && options.package && options.signType && options.paySign) {
      paymentParams = {
        timeStamp: options.timeStamp,
        nonceStr: options.nonceStr,
        package: options.package,
        signType: options.signType,
        paySign: options.paySign
      }
      console.log('使用高济H5直接传递的支付参数:', paymentParams)
    } else if (options.payParams) { // 方式2: 原有的payParams方式 (兼容旧方案)
      try {
        paymentParams = JSON.parse(decodeURIComponent(options.payParams))
        console.log('使用原有payParams方式的支付参数:', paymentParams)
      } catch (error) {
        console.error('解析payParams失败:', error)
        wx.showToast({
          title: '支付参数格式错误',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 2000)
        return
      }
    } else { // 无有效支付参数
      console.error('未找到有效的支付参数')
      wx.showToast({
        title: '缺少支付参数',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 2000)
      return
    }

    // 添加参数验证
    if (!paymentParams.timeStamp || !paymentParams.nonceStr || !paymentParams.package || !paymentParams.signType || !paymentParams.paySign) {
      console.error('支付参数不完整:', paymentParams)
      wx.showToast({
        title: '支付参数不完整',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 2000)
      return
    }

    // 调用微信支付
    wx.requestPayment({
      ...paymentParams,
      success: () => {
        console.log('微信支付成功')
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        })

        // 支付成功后的处理
        setTimeout(() => {
          // 如果有重定向地址，跳转到指定页面
          if (options.redirectUrl) {
            const redirectUrl = decodeURIComponent(options.redirectUrl)
            console.log('支付成功，跳转到:', redirectUrl)
            wx.redirectTo({
              url: `/pages/webView/index?url=${encodeURIComponent(redirectUrl)}`,
              fail: (e) => {
                console.log('支付成功但页面跳转失败', e)
                wx.navigateBack()
              }
            })
          } else {
            // 没有重定向地址，直接返回上一页
            console.log('支付成功，返回上一页')
            wx.navigateBack()
          }
        }, 1500)
      },
      fail: (error) => {
        console.log('微信支付失败:', error)
        if (error.errMsg && error.errMsg.includes('cancel')) {
          wx.showToast({
            title: '支付已取消',
            icon: 'none'
          })
        } else {
          wx.showToast({
            title: '支付失败',
            icon: 'none'
          })
        }

        // 支付失败后返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    })
  }
})
